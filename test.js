class ShopCalculator {
  /**
   * 构造函数
   * @param {Object} priceTable 价格表对象
   */
  constructor(priceTable) {
    this.priceTable = priceTable;
    this.consumers = {}; // 存储消费者及其消费项目
  }

  /**
   * 添加消费者
   * @param {string} consumerName 消费者名称
   */
  addConsumer(consumerName) {
    if (!this.consumers[consumerName]) {
      this.consumers[consumerName] = [];
    }
  }

  /**
   * 为消费者添加消费项目
   * @param {string} consumerName 消费者名称
   * @param {string} item 项目名称
   * @param {string} size 规格（如：大碗、中碗、小碗，可选）
   * @param {number} quantity 数量，默认为1
   */
  addItem(consumerName, item, size = null, quantity = 1) {
    // 确保消费者已存在
    this.addConsumer(consumerName);

    // 获取项目价格
    let price = this.getPrice(item, size);

    if (price === null) {
      console.error(`无法找到 ${item}${size ? "(" + size + ")" : ""} 的价格`);
      return;
    }

    // 添加到消费列表
    this.consumers[consumerName].push({
      item,
      size,
      quantity,
      unitPrice: price,
      totalPrice: price * quantity,
    });
  }

  /**
   * 获取项目价格
   * @param {string} item 项目名称
   * @param {string} size 规格（可选）
   * @returns {number|null} 价格，找不到返回null
   */
  getPrice(item, size) {
    if (!this.priceTable[item]) {
      return null;
    }

    // 如果项目有规格区分
    if (typeof this.priceTable[item] === "object") {
      if (size && this.priceTable[item][size]) {
        return this.priceTable[item][size];
      }
      return null;
    }

    // 如果项目没有规格区分
    return this.priceTable[item];
  }

  /**
   * 计算消费者的总消费金额
   * @param {string} consumerName 消费者名称
   * @returns {number} 总金额
   */
  calculateConsumerTotal(consumerName) {
    if (!this.consumers[consumerName]) {
      return 0;
    }

    return this.consumers[consumerName].reduce((total, item) => {
      return total + item.totalPrice;
    }, 0);
  }

  /**
   * 获取消费者的消费明细
   * @param {string} consumerName 消费者名称
   * @returns {Array} 消费明细数组
   */
  getConsumerDetails(consumerName) {
    return this.consumers[consumerName] || [];
  }

  /**
   * 计算所有消费者的总消费金额
   * @returns {number} 总金额
   */
  calculateAllTotal() {
    return Object.keys(this.consumers).reduce((total, consumerName) => {
      return total + this.calculateConsumerTotal(consumerName);
    }, 0);
  }
}

// 计算总金额函数
function calculateTotal() {
  // 1. 定义面馆价格表
  const noodleShopPriceTable = {
    牛肉面: {
      大碗: 18,
      中碗: 16,
      小碗: 14,
    },
    肥肠面: {
      大碗: 20,
      中碗: 18,
      小碗: 16,
    },
    牛肉饼: 10,
    奶茶: 12,
    套餐1: 38,
    套餐2: 40,
  };

  // 2. 创建计算器实例
  const calculator = new ShopCalculator(noodleShopPriceTable);

  // 3. 添加张三的消费项目
  calculator.addItem("张三", "套餐1");
  calculator.addItem("张三", "牛肉饼", null, 1);

  // 4. 添加李四的消费项目
  calculator.addItem("李四", "肥肠面", "中碗");
  calculator.addItem("李四", "奶茶", null, 2);

  // 5. 计算并显示结果
  const zhangsanTotal = calculator.calculateConsumerTotal("张三");
  const lisiTotal = calculator.calculateConsumerTotal("李四");

  document.getElementById(
    "zhangsanTotal"
  ).textContent = `张三应付总金额：${zhangsanTotal}元`;
  document.getElementById(
    "lisiTotal"
  ).textContent = `李四应付总金额：${lisiTotal}元`;
}
