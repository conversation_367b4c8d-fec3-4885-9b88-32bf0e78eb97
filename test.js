class ShopCalculator {
  constructor(priceTable) {
    this.priceTable = priceTable;
    this.consumers = {};
  }

  addConsumer(consumerName) {
    if (!this.consumers[consumerName]) {
      this.consumers[consumerName] = [];
    }
  }

  addItem(consumerName, item, size = null, quantity = 1) {
    this.addConsumer(consumerName);

    let price = this.getPrice(item, size);

    if (price === null) {
      console.error(`无法找到 ${item}${size ? "(" + size + ")" : ""} 的价格`);
      return;
    }

    this.consumers[consumerName].push({
      item,
      size,
      quantity,
      unitPrice: price,
      totalPrice: price * quantity,
    });
  }

  getPrice(item, size) {
    if (!this.priceTable[item]) {
      return null;
    }

    if (typeof this.priceTable[item] === "object") {
      if (size && this.priceTable[item][size]) {
        return this.priceTable[item][size];
      }
      return null;
    }

    return this.priceTable[item];
  }

  calculateConsumerTotal(consumerName) {
    if (!this.consumers[consumerName]) {
      return 0;
    }

    return this.consumers[consumerName].reduce((total, item) => {
      return total + item.totalPrice;
    }, 0);
  }

  getConsumerDetails(consumerName) {
    return this.consumers[consumerName] || [];
  }

  calculateAllTotal() {
    return Object.keys(this.consumers).reduce((total, consumerName) => {
      return total + this.calculateConsumerTotal(consumerName);
    }, 0);
  }
}

function calculateTotal() {
  const noodleShopPriceTable = {
    牛肉面: {
      大碗: 18,
      中碗: 16,
      小碗: 14,
    },
    肥肠面: {
      大碗: 20,
      中碗: 18,
      小碗: 16,
    },
    牛肉饼: 10,
    奶茶: 12,
    套餐1: 38,
    套餐2: 40,
  };

  const calculator = new ShopCalculator(noodleShopPriceTable);

  calculator.addItem("张三", "套餐1");
  calculator.addItem("张三", "牛肉饼", null, 1);

  calculator.addItem("李四", "肥肠面", "中碗");
  calculator.addItem("李四", "奶茶", null, 2);

  const zhangsanTotal = calculator.calculateConsumerTotal("张三");
  const lisiTotal = calculator.calculateConsumerTotal("李四");

  document.getElementById(
    "zhangsanTotal"
  ).textContent = `张三应付总金额：${zhangsanTotal}元`;
  document.getElementById(
    "lisiTotal"
  ).textContent = `李四应付总金额：${lisiTotal}元`;
}
